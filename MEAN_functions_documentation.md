# Документация утилитарных функций для плашки MEAN

## Обзор

В рамках первого этапа добавления плашки MEAN на генерируемые HTML сайты были созданы три утилитарные функции в файле `html_group.py`.

## Добавленные функции

### 1. `get_user_display_name(message) -> str`

**Назначение:** Получает отображаемое имя пользователя для показа на плашке.

**Параметры:**
- `message`: Объект сообщения Telegram

**Возвращает:** 
- `str`: Отображаемое имя пользователя

**Логика приоритета:**
1. `@username` (если есть username)
2. `"Имя Фамилия"` (если есть имя и фамилия)
3. `"Имя"` (если есть только имя)
4. `"Пользователь"` (fallback)

**Примеры использования:**
```python
# Пользователь с username
user_name = get_user_display_name(message)  # "@testuser"

# Пользователь без username, но с именем
user_name = get_user_display_name(message)  # "Иван Петров"

# Пользователь только с именем
user_name = get_user_display_name(message)  # "Иван"

# Пользователь без данных
user_name = get_user_display_name(message)  # "Пользователь"
```

### 2. `create_message_link(chat_id, message_id) -> str`

**Назначение:** Создает ссылку на сообщение в группе для кликабельного промпта.

**Параметры:**
- `chat_id`: ID чата (может быть отрицательным для групп)
- `message_id`: ID сообщения

**Возвращает:**
- `str`: Ссылка на сообщение в формате `https://t.me/c/{processed_chat_id}/{message_id}`

**Обработка chat_id:**
- Супергруппы (`-100...`): убирается минус и первые 3 цифры (100)
- Обычные группы (`-...`): убирается только минус
- Положительные ID: используются как есть

**Примеры использования:**
```python
# Супергруппа
link = create_message_link(-1001234567890, 12345)
# Результат: "https://t.me/c/1234567890/12345"

# Обычная группа
link = create_message_link(-123456789, 67890)
# Результат: "https://t.me/c/123456789/67890"
```

### 3. `truncate_prompt(prompt, max_words=8) -> str`

**Назначение:** Обрезает промпт для отображения на плашке, если он слишком длинный.

**Параметры:**
- `prompt`: Исходный промпт
- `max_words`: Максимальное количество слов (по умолчанию 8)

**Возвращает:**
- `str`: Обрезанная версия промпта для отображения

**Логика:**
- Если промпт ≤ `max_words`: возвращается полностью
- Если промпт > `max_words`: обрезается до `max_words` + "..."
- Пустой промпт возвращается как есть

**Примеры использования:**
```python
# Короткий промпт
display_prompt = truncate_prompt("создай красивый сайт")
# Результат: "создай красивый сайт"

# Длинный промпт
display_prompt = truncate_prompt("создай красивый современный сайт портфолио веб разработчика с анимациями")
# Результат: "создай красивый современный сайт портфолио веб разработчика с..."

# Кастомное количество слов
display_prompt = truncate_prompt("один два три четыре пять", max_words=3)
# Результат: "один два три..."
```

## Расположение в коде

Функции добавлены в файл `html_group.py` в строках 340-423, в разделе "УТИЛИТАРНЫЕ ФУНКЦИИ ДЛЯ ПЛАШКИ MEAN".

## Тестирование

Все функции протестированы с помощью файла `test_mean_functions.py`:
- 12 тестовых случаев
- Покрытие всех граничных случаев
- Проверка fallback сценариев
- Все тесты прошли успешно

## Готовность к следующему этапу

✅ Все утилитарные функции готовы для использования в Этапе 2 (создание системы overlay).

## Следующие шаги

После завершения Этапа 1, следующим шагом будет Этап 2: создание функции `generate_overlay_html()` для генерации HTML кода overlay с плашкой MEAN.
