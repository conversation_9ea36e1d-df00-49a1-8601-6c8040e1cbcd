#!/usr/bin/env python3
"""
Полный интеграционный тест для проверки всей системы overlay.
Тестирует все функции вместе: от получения данных пользователя до встраивания overlay.
"""

import sys
import os

# Добавляем путь к модулю html_group
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from html_group import (
    get_user_display_name, 
    create_message_link, 
    truncate_prompt, 
    generate_overlay_html, 
    inject_overlay_into_html
)


class MockUser:
    """Мок объект пользователя для тестирования."""
    def __init__(self, username=None, first_name=None, last_name=None):
        self.username = username
        self.first_name = first_name
        self.last_name = last_name


class MockMessage:
    """Мок объект сообщения для тестирования."""
    def __init__(self, user, chat_id, message_id):
        self.from_user = user
        self.chat = MockChat(chat_id)
        self.message_id = message_id


class MockChat:
    """Мок объект чата для тестирования."""
    def __init__(self, chat_id):
        self.id = chat_id


def test_full_integration():
    """Полный интеграционный тест всей системы overlay."""
    print("=== ПОЛНЫЙ ИНТЕГРАЦИОННЫЙ ТЕСТ ===")
    print()
    
    # Создаем тестовые данные
    user = MockUser(username="testuser")
    message = MockMessage(user, -1001234567890, 123)
    full_prompt = "Создай красивый интерактивный сайт-портфолио для веб-разработчика с анимациями, темной темой и адаптивным дизайном"
    
    # Тестовый HTML сайт
    original_html = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Website</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .hero {
            text-align: center;
            padding: 100px 0;
        }
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        .projects {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }
        .project {
            background: #2a2a2a;
            padding: 30px;
            border-radius: 10px;
            transition: transform 0.3s ease;
        }
        .project:hover {
            transform: translateY(-10px);
        }
    </style>
</head>
<body>
    <div class="container">
        <section class="hero">
            <h1>John Doe</h1>
            <p>Full Stack Web Developer</p>
            <button onclick="alert('Contact me!')">Get In Touch</button>
        </section>
        
        <section class="projects">
            <div class="project">
                <h3>E-commerce Platform</h3>
                <p>Modern online store with React and Node.js</p>
            </div>
            <div class="project">
                <h3>Task Management App</h3>
                <p>Productivity app with real-time collaboration</p>
            </div>
            <div class="project">
                <h3>Weather Dashboard</h3>
                <p>Beautiful weather app with interactive maps</p>
            </div>
        </section>
    </div>
    
    <script>
        // Простая анимация при загрузке
        document.addEventListener('DOMContentLoaded', function() {
            const projects = document.querySelectorAll('.project');
            projects.forEach((project, index) => {
                setTimeout(() => {
                    project.style.opacity = '1';
                    project.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>"""
    
    print("1. Извлекаем данные пользователя...")
    user_name = get_user_display_name(message)
    print(f"   Имя пользователя: {user_name}")
    
    print("2. Создаем ссылку на сообщение...")
    message_link = create_message_link(message.chat.id, message.message_id)
    print(f"   Ссылка: {message_link}")
    
    print("3. Обрабатываем промпт...")
    prompt_display = truncate_prompt(full_prompt)
    print(f"   Полный промпт: {full_prompt}")
    print(f"   Обрезанный промпт: {prompt_display}")
    
    print("4. Генерируем overlay HTML...")
    overlay_html = generate_overlay_html(user_name, prompt_display, full_prompt, message_link)
    print(f"   Overlay сгенерирован (длина: {len(overlay_html)} символов)")
    
    print("5. Встраиваем overlay в HTML...")
    final_html = inject_overlay_into_html(original_html, overlay_html)
    print(f"   Финальный HTML готов (длина: {len(final_html)} символов)")
    
    # Проверяем результат
    print("\n=== ПРОВЕРКА РЕЗУЛЬТАТА ===")
    
    # Проверяем, что overlay встроен
    assert 'mean-overlay-container' in final_html
    print("✅ Overlay контейнер найден в HTML")
    
    # Проверяем, что данные пользователя присутствуют
    assert user_name in final_html
    print(f"✅ Имя пользователя '{user_name}' найдено в HTML")
    
    # Проверяем, что промпт присутствует
    assert prompt_display in final_html
    print(f"✅ Обрезанный промпт найден в HTML")
    
    # Проверяем, что ссылка присутствует
    assert message_link in final_html
    print(f"✅ Ссылка на сообщение найдена в HTML")
    
    # Проверяем, что оригинальный контент сохранен
    assert 'John Doe' in final_html
    assert 'Full Stack Web Developer' in final_html
    assert 'E-commerce Platform' in final_html
    print("✅ Оригинальный контент сайта сохранен")
    
    # Проверяем, что overlay добавлен в правильное место (после <body>)
    body_index = final_html.find('<body>')
    overlay_index = final_html.find('mean-overlay-container')
    assert body_index < overlay_index
    print("✅ Overlay корректно размещен после тега <body>")
    
    # Проверяем наличие CSS стилей overlay
    assert 'mean-overlay-' in final_html
    assert 'position: fixed' in final_html
    assert 'z-index: 9999' in final_html
    print("✅ CSS стили overlay присутствуют")
    
    # Проверяем наличие JavaScript для автоскрытия
    assert 'setTimeout' in final_html
    assert '2000' in final_html  # 2 секунды
    print("✅ JavaScript для автоскрытия присутствует")
    
    print("\n=== СОЗДАНИЕ ТЕСТОВОГО ФАЙЛА ===")
    
    # Сохраняем результат в файл для визуальной проверки
    with open('test_full_integration_result.html', 'w', encoding='utf-8') as f:
        f.write(final_html)
    
    print("✅ Результат сохранен в файл 'test_full_integration_result.html'")
    print("   Откройте этот файл в браузере для визуальной проверки")
    
    print("\n" + "=" * 60)
    print("🎉 ПОЛНЫЙ ИНТЕГРАЦИОННЫЙ ТЕСТ ПРОЙДЕН УСПЕШНО!")
    print("Все компоненты системы overlay работают корректно:")
    print("- Извлечение данных пользователя ✅")
    print("- Создание ссылок на сообщения ✅") 
    print("- Обработка промптов ✅")
    print("- Генерация overlay HTML ✅")
    print("- Встраивание overlay в HTML ✅")
    print("- Сохранение оригинального контента ✅")
    print("- Корректное позиционирование ✅")
    print("- Стили и анимации ✅")
    print("=" * 60)


def test_different_user_types():
    """Тест с разными типами пользователей."""
    print("\n=== ТЕСТ РАЗНЫХ ТИПОВ ПОЛЬЗОВАТЕЛЕЙ ===")
    
    test_cases = [
        ("Пользователь с username", MockUser(username="johndoe")),
        ("Пользователь с именем и фамилией", MockUser(first_name="John", last_name="Doe")),
        ("Пользователь только с именем", MockUser(first_name="John")),
        ("Пользователь без данных", MockUser()),
    ]
    
    simple_html = """<!DOCTYPE html>
<html><head><title>Test</title></head>
<body><h1>Test Page</h1></body></html>"""
    
    for description, user in test_cases:
        print(f"\nТестируем: {description}")
        
        message = MockMessage(user, -1001234567890, 123)
        user_name = get_user_display_name(message)
        message_link = create_message_link(message.chat.id, message.message_id)
        prompt = "Тестовый промпт"
        
        overlay_html = generate_overlay_html(user_name, prompt, prompt, message_link)
        final_html = inject_overlay_into_html(simple_html, overlay_html)
        
        assert user_name in final_html
        assert 'mean-overlay-container' in final_html
        print(f"   ✅ Успешно: имя '{user_name}' корректно обработано")
    
    print("\n✅ Все типы пользователей обработаны корректно")


if __name__ == "__main__":
    print("ПОЛНОЕ ИНТЕГРАЦИОННОЕ ТЕСТИРОВАНИЕ СИСТЕМЫ OVERLAY")
    print("=" * 70)
    
    try:
        test_full_integration()
        test_different_user_types()
        
        print("\n" + "=" * 70)
        print("🚀 ВСЕ ИНТЕГРАЦИОННЫЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("Система готова к использованию в продакшене.")
        
    except Exception as e:
        print(f"\n❌ ОШИБКА В ИНТЕГРАЦИОННЫХ ТЕСТАХ: {e}")
        import traceback
        traceback.print_exc()
