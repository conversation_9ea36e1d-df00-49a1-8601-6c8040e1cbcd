<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Website</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .hero {
            text-align: center;
            padding: 100px 0;
        }
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        .projects {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }
        .project {
            background: #2a2a2a;
            padding: 30px;
            border-radius: 10px;
            transition: transform 0.3s ease;
        }
        .project:hover {
            transform: translateY(-10px);
        }
    </style>
</head>
<body>

<!-- MEAN Overlay Start -->
<div id="mean-overlay-container" style="
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: 'Press Start 2P', cursive;
">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">

    <div class="mean-overlay-prompt-container" style="
        width: 550px;
        height: 550px;
        max-width: 90vw;
        max-height: 90vh;
        background-color: #282828;
        border: 8px solid #FFD700;
        box-shadow: 10px 10px 0px #a18700;
        padding: 30px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        color: #E0E0E0;
        line-height: 1.8;
    ">
        <!-- Верхний блок: заголовок и промпт -->
        <div>
            <p class="mean-overlay-prompt-header" style="
                font-size: 1.2em;
                color: #FFD700;
                text-align: left;
                margin: 0;
            ">Промпт:</p>
            <p class="mean-overlay-prompt-body" style="
                font-size: 1em;
                text-align: center;
                white-space: pre-wrap;
                word-break: break-word;
                margin: 20px 0;
            ">
                <a href="https://t.me/c/1234567890/123" target="_blank" title="Создай красивый интерактивный сайт-портфолио для веб-разработчика с анимациями, темной темой и адаптивным дизайном" style="
                    color: #E0E0E0;
                    text-decoration: none;
                    cursor: pointer;
                ">Создай красивый интерактивный сайт-портфолио для веб-разработчика с анимациями,...</a>
            </p>
        </div>

        <!-- Нижний блок: подпись -->
        <div>
            <p class="mean-overlay-footer-text" style="
                text-align: right;
                font-size: 0.9em;
                color: #999;
                margin: 0;
            ">
                по просьбе @testuser<br>
                by <a href="https://t.me/UseShBot" target="_blank" style="
                    color: #FFD700;
                    text-decoration: none;
                ">@UseShBot</a>
            </p>
        </div>
    </div>
</div>

<script>
(function() {
    // Автоматическое скрытие overlay через 2 секунды
    setTimeout(function() {
        var overlay = document.getElementById('mean-overlay-container');
        if (overlay) {
            // Анимация fade-out
            overlay.style.transition = 'opacity 0.5s ease-out';
            overlay.style.opacity = '0';

            // Удаление из DOM после анимации
            setTimeout(function() {
                if (overlay.parentNode) {
                    overlay.parentNode.removeChild(overlay);
                }
            }, 500);
        }
    }, 2000);

    // Возможность закрыть overlay кликом по фону
    var overlay = document.getElementById('mean-overlay-container');
    if (overlay) {
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) {
                overlay.style.transition = 'opacity 0.3s ease-out';
                overlay.style.opacity = '0';
                setTimeout(function() {
                    if (overlay.parentNode) {
                        overlay.parentNode.removeChild(overlay);
                    }
                }, 300);
            }
        });
    }
})();
</script>
<!-- MEAN Overlay End -->


    <div class="container">
        <section class="hero">
            <h1>John Doe</h1>
            <p>Full Stack Web Developer</p>
            <button onclick="alert('Contact me!')">Get In Touch</button>
        </section>
        
        <section class="projects">
            <div class="project">
                <h3>E-commerce Platform</h3>
                <p>Modern online store with React and Node.js</p>
            </div>
            <div class="project">
                <h3>Task Management App</h3>
                <p>Productivity app with real-time collaboration</p>
            </div>
            <div class="project">
                <h3>Weather Dashboard</h3>
                <p>Beautiful weather app with interactive maps</p>
            </div>
        </section>
    </div>
    
    <script>
        // Простая анимация при загрузке
        document.addEventListener('DOMContentLoaded', function() {
            const projects = document.querySelectorAll('.project');
            projects.forEach((project, index) => {
                setTimeout(() => {
                    project.style.opacity = '1';
                    project.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>