#!/usr/bin/env python3
"""
Тест функции inject_overlay_into_html для проверки корректности встраивания overlay.
"""

import sys
import os

# Добавляем путь к модулю html_group
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from html_group import inject_overlay_into_html


def test_inject_with_body():
    """Тест встраивания overlay в HTML с тегом <body>."""
    print("=== ТЕСТ 1: HTML с тегом <body> ===")
    
    original_html = """<!DOCTYPE html>
<html lang="en">
<head>
    <title>Test Page</title>
</head>
<body>
    <h1>Hello World</h1>
    <p>This is a test page.</p>
</body>
</html>"""
    
    overlay_html = '<div id="overlay">Test Overlay</div>'
    
    result = inject_overlay_into_html(original_html, overlay_html)
    
    print("Оригинальный HTML:")
    print(original_html)
    print("\nOverlay HTML:")
    print(overlay_html)
    print("\nРезультат:")
    print(result)
    
    # Проверяем, что overlay добавлен после <body>
    assert '<body>\n<div id="overlay">Test Overlay</div>' in result
    assert '<h1>Hello World</h1>' in result
    print("✅ Тест пройден: overlay корректно встроен после <body>")
    print()


def test_inject_with_body_attributes():
    """Тест встраивания overlay в HTML с тегом <body> с атрибутами."""
    print("=== ТЕСТ 2: HTML с тегом <body> с атрибутами ===")
    
    original_html = """<!DOCTYPE html>
<html>
<head><title>Test</title></head>
<body class="main-page" style="margin: 0;">
    <div>Content</div>
</body>
</html>"""
    
    overlay_html = '<div class="overlay">Overlay Content</div>'
    
    result = inject_overlay_into_html(original_html, overlay_html)
    
    print("Оригинальный HTML:")
    print(original_html)
    print("\nOverlay HTML:")
    print(overlay_html)
    print("\nРезультат:")
    print(result)
    
    # Проверяем, что overlay добавлен после <body> с атрибутами
    assert 'class="main-page" style="margin: 0;">\n<div class="overlay">Overlay Content</div>' in result
    assert '<div>Content</div>' in result
    print("✅ Тест пройден: overlay корректно встроен после <body> с атрибутами")
    print()


def test_inject_without_body():
    """Тест встраивания overlay в HTML без тега <body> (fallback к <html>)."""
    print("=== ТЕСТ 3: HTML без тега <body> (fallback к <html>) ===")
    
    original_html = """<!DOCTYPE html>
<html lang="en">
<head>
    <title>No Body Tag</title>
</head>
<div>Direct content without body</div>
</html>"""
    
    overlay_html = '<div id="fallback-overlay">Fallback Overlay</div>'
    
    result = inject_overlay_into_html(original_html, overlay_html)
    
    print("Оригинальный HTML:")
    print(original_html)
    print("\nOverlay HTML:")
    print(overlay_html)
    print("\nРезультат:")
    print(result)
    
    # Проверяем, что overlay добавлен после <html>
    assert '<html lang="en">\n<div id="fallback-overlay">Fallback Overlay</div>' in result
    assert '<div>Direct content without body</div>' in result
    print("✅ Тест пройден: overlay корректно встроен после <html> (fallback)")
    print()


def test_inject_minimal_html():
    """Тест встраивания overlay в минимальный HTML (fallback к DOCTYPE)."""
    print("=== ТЕСТ 4: Минимальный HTML (fallback к DOCTYPE) ===")
    
    original_html = """<!DOCTYPE html>
<title>Minimal</title>
<h1>Minimal HTML</h1>"""
    
    overlay_html = '<div class="minimal-overlay">Minimal Overlay</div>'
    
    result = inject_overlay_into_html(original_html, overlay_html)
    
    print("Оригинальный HTML:")
    print(original_html)
    print("\nOverlay HTML:")
    print(overlay_html)
    print("\nРезультат:")
    print(result)
    
    # Проверяем, что overlay добавлен после DOCTYPE
    assert '<!DOCTYPE html>\n<div class="minimal-overlay">Minimal Overlay</div>' in result
    assert '<h1>Minimal HTML</h1>' in result
    print("✅ Тест пройден: overlay корректно встроен после DOCTYPE (fallback)")
    print()


def test_inject_no_doctype():
    """Тест встраивания overlay в HTML без DOCTYPE (последний fallback)."""
    print("=== ТЕСТ 5: HTML без DOCTYPE (последний fallback) ===")
    
    original_html = """<h1>Raw HTML</h1>
<p>No DOCTYPE, no html tag, no body tag</p>"""
    
    overlay_html = '<div class="raw-overlay">Raw Overlay</div>'
    
    result = inject_overlay_into_html(original_html, overlay_html)
    
    print("Оригинальный HTML:")
    print(original_html)
    print("\nOverlay HTML:")
    print(overlay_html)
    print("\nРезультат:")
    print(result)
    
    # Проверяем, что overlay добавлен в начало
    assert result.startswith('<div class="raw-overlay">Raw Overlay</div>')
    assert '<h1>Raw HTML</h1>' in result
    print("✅ Тест пройден: overlay корректно добавлен в начало (последний fallback)")
    print()


def test_case_insensitive():
    """Тест нечувствительности к регистру тегов."""
    print("=== ТЕСТ 6: Нечувствительность к регистру ===")
    
    original_html = """<!DOCTYPE HTML>
<HTML>
<HEAD><TITLE>Uppercase</TITLE></HEAD>
<BODY>
    <H1>Uppercase HTML</H1>
</BODY>
</HTML>"""
    
    overlay_html = '<div>Case Test Overlay</div>'
    
    result = inject_overlay_into_html(original_html, overlay_html)
    
    print("Оригинальный HTML:")
    print(original_html)
    print("\nOverlay HTML:")
    print(overlay_html)
    print("\nРезультат:")
    print(result)
    
    # Проверяем, что overlay добавлен после <BODY>
    assert '<BODY>\n<div>Case Test Overlay</div>' in result
    assert '<H1>Uppercase HTML</H1>' in result
    print("✅ Тест пройден: функция корректно работает с разным регистром")
    print()


if __name__ == "__main__":
    print("ТЕСТИРОВАНИЕ ФУНКЦИИ inject_overlay_into_html")
    print("=" * 60)
    print()
    
    try:
        test_inject_with_body()
        test_inject_with_body_attributes()
        test_inject_without_body()
        test_inject_minimal_html()
        test_inject_no_doctype()
        test_case_insensitive()
        
        print("=" * 60)
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!")
        print("Функция inject_overlay_into_html работает корректно во всех сценариях.")
        
    except Exception as e:
        print(f"❌ ОШИБКА В ТЕСТАХ: {e}")
        import traceback
        traceback.print_exc()
