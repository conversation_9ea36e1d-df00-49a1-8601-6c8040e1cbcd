"""
Тестирование утилитарных функций для плашки MEAN.
Этап 1: Проверка функций get_user_display_name, create_message_link, truncate_prompt
"""

import sys
import os

# Добавляем текущую директорию в путь для импорта
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Импортируем функции из html_group.py
from html_group import get_user_display_name, create_message_link, truncate_prompt


class MockUser:
    """Мок-объект для пользователя Telegram"""
    def __init__(self, username=None, first_name=None, last_name=None):
        self.username = username
        self.first_name = first_name
        self.last_name = last_name


class MockMessage:
    """Мок-объект для сообщения Telegram"""
    def __init__(self, user):
        self.from_user = user


def test_get_user_display_name():
    """Тестирование функции get_user_display_name"""
    print("=== ТЕСТ get_user_display_name ===")
    
    # Тест 1: Пользователь с username
    user1 = MockUser(username="testuser", first_name="Иван", last_name="Петров")
    message1 = MockMessage(user1)
    result1 = get_user_display_name(message1)
    print(f"Тест 1 (username): {result1}")
    assert result1 == "@testuser", f"Ожидалось '@testuser', получено '{result1}'"
    
    # Тест 2: Пользователь с именем и фамилией, но без username
    user2 = MockUser(username=None, first_name="Иван", last_name="Петров")
    message2 = MockMessage(user2)
    result2 = get_user_display_name(message2)
    print(f"Тест 2 (имя + фамилия): {result2}")
    assert result2 == "Иван Петров", f"Ожидалось 'Иван Петров', получено '{result2}'"
    
    # Тест 3: Пользователь только с именем
    user3 = MockUser(username=None, first_name="Иван", last_name=None)
    message3 = MockMessage(user3)
    result3 = get_user_display_name(message3)
    print(f"Тест 3 (только имя): {result3}")
    assert result3 == "Иван", f"Ожидалось 'Иван', получено '{result3}'"
    
    # Тест 4: Пользователь без данных (fallback)
    user4 = MockUser(username=None, first_name=None, last_name=None)
    message4 = MockMessage(user4)
    result4 = get_user_display_name(message4)
    print(f"Тест 4 (fallback): {result4}")
    assert result4 == "Пользователь", f"Ожидалось 'Пользователь', получено '{result4}'"
    
    print("✅ Все тесты get_user_display_name прошли успешно!\n")


def test_create_message_link():
    """Тестирование функции create_message_link"""
    print("=== ТЕСТ create_message_link ===")
    
    # Тест 1: Супергруппа (-100...)
    chat_id1 = -1001234567890
    message_id1 = 12345
    result1 = create_message_link(chat_id1, message_id1)
    expected1 = "https://t.me/c/1234567890/12345"
    print(f"Тест 1 (супергруппа): {result1}")
    assert result1 == expected1, f"Ожидалось '{expected1}', получено '{result1}'"
    
    # Тест 2: Обычная группа (-...)
    chat_id2 = -123456789
    message_id2 = 67890
    result2 = create_message_link(chat_id2, message_id2)
    expected2 = "https://t.me/c/123456789/67890"
    print(f"Тест 2 (обычная группа): {result2}")
    assert result2 == expected2, f"Ожидалось '{expected2}', получено '{result2}'"
    
    # Тест 3: Положительный ID (теоретический случай)
    chat_id3 = 123456789
    message_id3 = 11111
    result3 = create_message_link(chat_id3, message_id3)
    expected3 = "https://t.me/c/123456789/11111"
    print(f"Тест 3 (положительный ID): {result3}")
    assert result3 == expected3, f"Ожидалось '{expected3}', получено '{result3}'"
    
    print("✅ Все тесты create_message_link прошли успешно!\n")


def test_truncate_prompt():
    """Тестирование функции truncate_prompt"""
    print("=== ТЕСТ truncate_prompt ===")
    
    # Тест 1: Короткий промпт (меньше 8 слов)
    prompt1 = "создай красивый сайт портфолио"
    result1 = truncate_prompt(prompt1)
    print(f"Тест 1 (короткий): '{result1}'")
    assert result1 == prompt1, f"Ожидалось '{prompt1}', получено '{result1}'"
    
    # Тест 2: Промпт ровно 8 слов
    prompt2 = "создай красивый сайт портфолио веб разработчика современный"
    result2 = truncate_prompt(prompt2)
    print(f"Тест 2 (8 слов): '{result2}'")
    assert result2 == prompt2, f"Ожидалось '{prompt2}', получено '{result2}'"
    
    # Тест 3: Длинный промпт (больше 8 слов)
    prompt3 = "создай красивый современный сайт портфолио веб разработчика с анимациями и интерактивными элементами"
    result3 = truncate_prompt(prompt3)
    expected3 = "создай красивый современный сайт портфолио веб разработчика с..."
    print(f"Тест 3 (длинный): '{result3}'")
    assert result3 == expected3, f"Ожидалось '{expected3}', получено '{result3}'"
    
    # Тест 4: Пустой промпт
    prompt4 = ""
    result4 = truncate_prompt(prompt4)
    print(f"Тест 4 (пустой): '{result4}'")
    assert result4 == "", f"Ожидалось '', получено '{result4}'"
    
    # Тест 5: Кастомное количество слов
    prompt5 = "один два три четыре пять шесть"
    result5 = truncate_prompt(prompt5, max_words=4)
    expected5 = "один два три четыре..."
    print(f"Тест 5 (кастомное max_words=4): '{result5}'")
    assert result5 == expected5, f"Ожидалось '{expected5}', получено '{result5}'"
    
    print("✅ Все тесты truncate_prompt прошли успешно!\n")


def main():
    """Запуск всех тестов"""
    print("🧪 ЗАПУСК ТЕСТОВ УТИЛИТАРНЫХ ФУНКЦИЙ MEAN\n")
    
    try:
        test_get_user_display_name()
        test_create_message_link()
        test_truncate_prompt()
        
        print("🎉 ВСЕ ТЕСТЫ ПРОШЛИ УСПЕШНО!")
        print("✅ Этап 1 выполнен: утилитарные функции работают корректно")
        
    except Exception as e:
        print(f"❌ ОШИБКА В ТЕСТАХ: {e}")
        return False
    
    return True


if __name__ == "__main__":
    main()
