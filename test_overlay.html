<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест MEAN Overlay</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background-color: #f0f0f0;
        }
        .test-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
        }
        p {
            line-height: 1.6;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="test-content">
        <h1>Тестовый сайт для проверки MEAN Overlay</h1>
        <p>Это обычный HTML сайт. При открытии должна появиться плашка MEAN на 2 секунды.</p>
        <p>Плашка должна:</p>
        <ul>
            <li>Появиться поверх всего контента</li>
            <li>Показать информацию о пользователе и промпте</li>
            <li>Иметь кликабельную ссылку на промпт</li>
            <li>Автоматически исчезнуть через 2 секунды</li>
            <li>Закрываться при клике по фону</li>
        </ul>
        <p>Если вы видите этот текст сразу без плашки - что-то пошло не так.</p>
    </div>

<!-- MEAN Overlay Start -->
<div id="mean-overlay-container" style="
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: 'Press Start 2P', cursive;
">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
    
    <div class="mean-overlay-prompt-container" style="
        width: 550px;
        height: 550px;
        max-width: 90vw;
        max-height: 90vh;
        background-color: #282828;
        border: 8px solid #FFD700;
        box-shadow: 10px 10px 0px #a18700;
        padding: 30px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        color: #E0E0E0;
        line-height: 1.8;
    ">
        <!-- Верхний блок: заголовок и промпт -->
        <div>
            <p class="mean-overlay-prompt-header" style="
                font-size: 1.2em;
                color: #FFD700;
                text-align: left;
                margin: 0;
            ">Промпт:</p>
            <p class="mean-overlay-prompt-body" style="
                font-size: 1em;
                text-align: center;
                white-space: pre-wrap;
                word-break: break-word;
                margin: 20px 0;
            ">
                <a href="https://t.me/c/1234567890/123" target="_blank" title="Создай красивый сайт с анимациями и интерактивными элементами для портфолио веб-разработчика" style="
                    color: #E0E0E0;
                    text-decoration: none;
                    cursor: pointer;
                ">Создай красивый сайт с анимациями и интерактивными...</a>
            </p>
        </div>
        
        <!-- Нижний блок: подпись -->
        <div>
            <p class="mean-overlay-footer-text" style="
                text-align: right;
                font-size: 0.9em;
                color: #999;
                margin: 0;
            ">
                по просьбе @testuser<br>
                by <a href="https://t.me/UseShBot" target="_blank" style="
                    color: #FFD700;
                    text-decoration: none;
                ">@UseShBot</a>
            </p>
        </div>
    </div>
</div>

<script>
(function() {
    // Автоматическое скрытие overlay через 2 секунды
    setTimeout(function() {
        var overlay = document.getElementById('mean-overlay-container');
        if (overlay) {
            // Анимация fade-out
            overlay.style.transition = 'opacity 0.5s ease-out';
            overlay.style.opacity = '0';
            
            // Удаление из DOM после анимации
            setTimeout(function() {
                if (overlay.parentNode) {
                    overlay.parentNode.removeChild(overlay);
                }
            }, 500);
        }
    }, 2000);
    
    // Возможность закрыть overlay кликом по фону
    var overlay = document.getElementById('mean-overlay-container');
    if (overlay) {
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) {
                overlay.style.transition = 'opacity 0.3s ease-out';
                overlay.style.opacity = '0';
                setTimeout(function() {
                    if (overlay.parentNode) {
                        overlay.parentNode.removeChild(overlay);
                    }
                }, 300);
            }
        });
    }
})();
</script>
<!-- MEAN Overlay End -->

</body>
</html>
