#!/usr/bin/env python3
"""
Тест функции generate_overlay_html для проверки корректности генерации overlay.
"""

import sys
import os

# Добавляем путь к модулю html_group
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from html_group import generate_overlay_html, get_user_display_name, create_message_link, truncate_prompt


def test_generate_overlay_html():
    """Тестирует функцию generate_overlay_html с различными входными данными."""
    
    print("=== ТЕСТ ФУНКЦИИ generate_overlay_html ===\n")
    
    # Тест 1: Обычные данные
    print("Тест 1: Обычные данные")
    user_name = "@testuser"
    prompt_display = "Создай красивый сайт с анимациями..."
    prompt_full = "Создай красивый сайт с анимациями и интерактивными элементами для портфолио веб-разработчика"
    message_link = "https://t.me/c/1234567890/123"
    
    overlay_html = generate_overlay_html(user_name, prompt_display, prompt_full, message_link)
    
    # Проверяем наличие ключевых элементов
    assert "mean-overlay-container" in overlay_html, "Отсутствует контейнер overlay"
    assert user_name in overlay_html, "Отсутствует имя пользователя"
    assert prompt_display in overlay_html, "Отсутствует отображаемый промпт"
    assert message_link in overlay_html, "Отсутствует ссылка на сообщение"
    assert "setTimeout" in overlay_html, "Отсутствует JavaScript для автоскрытия"
    assert "z-index: 9999" in overlay_html, "Неправильный z-index"
    assert "#FFD700" in overlay_html, "Отсутствует желтый цвет"
    
    print("✅ Тест 1 пройден")
    
    # Тест 2: Данные с HTML символами (проверка экранирования)
    print("\nТест 2: Данные с HTML символами")
    user_name_html = "<script>alert('xss')</script>"
    prompt_display_html = "Промпт с <b>HTML</b> & символами"
    prompt_full_html = "Полный промпт с <script>alert('xss')</script> и & символами"
    message_link_html = "https://t.me/c/123/456?param=<script>"
    
    overlay_html_safe = generate_overlay_html(user_name_html, prompt_display_html, prompt_full_html, message_link_html)
    
    # Проверяем, что HTML символы экранированы
    assert "&lt;script&gt;" in overlay_html_safe, "HTML не экранирован в имени пользователя"
    assert "&lt;b&gt;" in overlay_html_safe, "HTML не экранирован в промпте"
    assert "&amp;" in overlay_html_safe, "Амперсанд не экранирован"
    assert "<script>alert('xss')</script>" not in overlay_html_safe, "XSS уязвимость!"
    
    print("✅ Тест 2 пройден (HTML экранирование работает)")
    
    # Тест 3: Пустые данные
    print("\nТест 3: Пустые данные")
    overlay_html_empty = generate_overlay_html("", "", "", "")
    
    assert "mean-overlay-container" in overlay_html_empty, "Overlay должен генерироваться даже с пустыми данными"
    assert "по просьбе " in overlay_html_empty, "Должна быть подпись"
    
    print("✅ Тест 3 пройден")
    
    # Тест 4: Длинные данные
    print("\nТест 4: Длинные данные")
    long_user_name = "Очень_длинное_имя_пользователя_которое_может_сломать_верстку"
    long_prompt = "Очень длинный промпт " * 50
    
    overlay_html_long = generate_overlay_html(long_user_name, long_prompt, long_prompt, message_link)
    
    assert long_user_name in overlay_html_long, "Длинное имя должно присутствовать"
    assert "word-break: break-word" in overlay_html_long, "Должна быть обработка переноса слов"
    
    print("✅ Тест 4 пройден")
    
    print("\n=== ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО! ===")


def test_integration_with_utility_functions():
    """Тестирует интеграцию generate_overlay_html с утилитарными функциями."""
    
    print("\n=== ТЕСТ ИНТЕГРАЦИИ С УТИЛИТАРНЫМИ ФУНКЦИЯМИ ===\n")
    
    # Создаем mock объект сообщения
    class MockUser:
        def __init__(self, username=None, first_name=None, last_name=None):
            self.username = username
            self.first_name = first_name
            self.last_name = last_name
    
    class MockMessage:
        def __init__(self, user, chat_id, message_id):
            self.from_user = user
            self.chat = type('obj', (object,), {'id': chat_id})
            self.message_id = message_id
    
    # Тест полной интеграции
    user = MockUser(username="testuser")
    message = MockMessage(user, -1001234567890, 123)
    full_prompt = "Создай красивый сайт с анимациями и интерактивными элементами для портфолио веб-разработчика"
    
    # Используем утилитарные функции
    user_name = get_user_display_name(message)
    message_link = create_message_link(message.chat.id, message.message_id)
    prompt_display = truncate_prompt(full_prompt)
    
    # Генерируем overlay
    overlay_html = generate_overlay_html(user_name, prompt_display, full_prompt, message_link)
    
    # Проверяем результат
    assert "@testuser" in overlay_html, "Неправильное имя пользователя"
    assert "1234567890" in overlay_html, "Неправильная ссылка на сообщение"
    assert "..." in overlay_html, "Промпт не обрезан"
    assert len(prompt_display.split()) <= 8, "Промпт не обрезан до 8 слов"
    
    print("✅ Интеграция с утилитарными функциями работает корректно")
    
    print("\n=== ИНТЕГРАЦИОННЫЙ ТЕСТ ПРОЙДЕН! ===")


def create_test_html_file():
    """Создает тестовый HTML файл с реальными данными."""
    
    print("\n=== СОЗДАНИЕ ТЕСТОВОГО HTML ФАЙЛА ===\n")
    
    # Генерируем overlay с реальными данными
    user_name = "@testuser"
    full_prompt = "Создай красивый интерактивный сайт-портфолио для веб-разработчика с анимациями, темной темой и адаптивным дизайном"
    prompt_display = truncate_prompt(full_prompt)
    message_link = create_message_link(-1001234567890, 123)
    
    overlay_html = generate_overlay_html(user_name, prompt_display, full_prompt, message_link)
    
    # Создаем полный HTML файл
    full_html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест MEAN Overlay - Реальные данные</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        .content {{
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 800px;
            margin: 0 auto;
        }}
        h1 {{
            color: #333;
            text-align: center;
        }}
        .info {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }}
    </style>
</head>
<body>
    <div class="content">
        <h1>🎯 Тест MEAN Overlay</h1>
        <div class="info">
            <h3>Тестируемые параметры:</h3>
            <p><strong>Пользователь:</strong> {user_name}</p>
            <p><strong>Полный промпт:</strong> {full_prompt}</p>
            <p><strong>Отображаемый промпт:</strong> {prompt_display}</p>
            <p><strong>Ссылка:</strong> {message_link}</p>
        </div>
        <p>Overlay должен появиться на 2 секунды при загрузке страницы.</p>
    </div>

{overlay_html}

</body>
</html>"""
    
    # Сохраняем файл
    with open("test_overlay_real_data.html", "w", encoding="utf-8") as f:
        f.write(full_html)
    
    print("✅ Тестовый HTML файл создан: test_overlay_real_data.html")
    print(f"📝 Промпт обрезан с {len(full_prompt.split())} до {len(prompt_display.split())} слов")
    print(f"🔗 Ссылка на сообщение: {message_link}")


if __name__ == "__main__":
    try:
        test_generate_overlay_html()
        test_integration_with_utility_functions()
        create_test_html_file()
        print("\n🎉 ВСЕ ТЕСТЫ УСПЕШНО ЗАВЕРШЕНЫ!")
        
    except Exception as e:
        print(f"\n❌ ОШИБКА В ТЕСТАХ: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
