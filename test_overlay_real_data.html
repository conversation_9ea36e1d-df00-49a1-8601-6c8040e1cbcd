<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест MEAN Overlay - Реальные данные</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="content">
        <h1>🎯 Тест MEAN Overlay</h1>
        <div class="info">
            <h3>Тестируемые параметры:</h3>
            <p><strong>Пользователь:</strong> @testuser</p>
            <p><strong>Полный промпт:</strong> Создай красивый интерактивный сайт-портфолио для веб-разработчика с анимациями, темной темой и адаптивным дизайном</p>
            <p><strong>Отображаемый промпт:</strong> Создай красивый интерактивный сайт-портфолио для веб-разработчика с анимациями,...</p>
            <p><strong>Ссылка:</strong> https://t.me/c/1234567890/123</p>
        </div>
        <p>Overlay должен появиться на 2 секунды при загрузке страницы.</p>
    </div>


<!-- MEAN Overlay Start -->
<div id="mean-overlay-container" style="
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: 'Press Start 2P', cursive;
">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">

    <div class="mean-overlay-prompt-container" style="
        width: 550px;
        height: 550px;
        max-width: 90vw;
        max-height: 90vh;
        background-color: #282828;
        border: 8px solid #FFD700;
        box-shadow: 10px 10px 0px #a18700;
        padding: 30px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        color: #E0E0E0;
        line-height: 1.8;
    ">
        <!-- Верхний блок: заголовок и промпт -->
        <div>
            <p class="mean-overlay-prompt-header" style="
                font-size: 1.2em;
                color: #FFD700;
                text-align: left;
                margin: 0;
            ">Промпт:</p>
            <p class="mean-overlay-prompt-body" style="
                font-size: 1em;
                text-align: center;
                white-space: pre-wrap;
                word-break: break-word;
                margin: 20px 0;
            ">
                <a href="https://t.me/c/1234567890/123" target="_blank" title="Создай красивый интерактивный сайт-портфолио для веб-разработчика с анимациями, темной темой и адаптивным дизайном" style="
                    color: #E0E0E0;
                    text-decoration: none;
                    cursor: pointer;
                ">Создай красивый интерактивный сайт-портфолио для веб-разработчика с анимациями,...</a>
            </p>
        </div>

        <!-- Нижний блок: подпись -->
        <div>
            <p class="mean-overlay-footer-text" style="
                text-align: right;
                font-size: 0.9em;
                color: #999;
                margin: 0;
            ">
                по просьбе @testuser<br>
                by <a href="https://t.me/UseShBot" target="_blank" style="
                    color: #FFD700;
                    text-decoration: none;
                ">@UseShBot</a>
            </p>
        </div>
    </div>
</div>

<script>
(function() {
    // Автоматическое скрытие overlay через 2 секунды
    setTimeout(function() {
        var overlay = document.getElementById('mean-overlay-container');
        if (overlay) {
            // Анимация fade-out
            overlay.style.transition = 'opacity 0.5s ease-out';
            overlay.style.opacity = '0';

            // Удаление из DOM после анимации
            setTimeout(function() {
                if (overlay.parentNode) {
                    overlay.parentNode.removeChild(overlay);
                }
            }, 500);
        }
    }, 2000);

    // Возможность закрыть overlay кликом по фону
    var overlay = document.getElementById('mean-overlay-container');
    if (overlay) {
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) {
                overlay.style.transition = 'opacity 0.3s ease-out';
                overlay.style.opacity = '0';
                setTimeout(function() {
                    if (overlay.parentNode) {
                        overlay.parentNode.removeChild(overlay);
                    }
                }, 300);
            }
        });
    }
})();
</script>
<!-- MEAN Overlay End -->


</body>
</html>