ПЛАН ДОБАВЛЕНИЯ ПЛАШКИ MEAN НА ГЕНЕРИРУЕМЫЕ HTML САЙТЫ
================================================================

ЦЕЛЬ: Добавить плашку из MEAN.html на все сайты, генерируемые командой /html
- Плашка показывается первые 2 секунды при открытии сайта
- Содержит информацию о пользователе и промпте
- Промпт кликабельный и ведет на исходное сообщение
- Стиль соответствует MEAN.html

================================================================
ЭТАП 1: СОЗДАНИЕ УТИЛИТАРНЫХ ФУНКЦИЙ
================================================================

ЗАДАЧИ:
1. Создать функцию get_user_display_name(message)
   - Получает username пользователя или имя+фамилия если username нет
   - Приоритет: username -> "имя фамилия" -> "имя" -> "Пользователь"

2. Создать функцию create_message_link(chat_id, message_id)
   - Создает ссылку на сообщение в группе
   - Формат: https://t.me/c/{chat_id_без_минуса_и_первой_цифры}/{message_id}
   - Обработка отрицательного chat_id для групп

3. Создать функцию truncate_prompt(prompt, max_words=8)
   - Обрезает промпт если больше 8 слов
   - Добавляет "..." в конце при обрезке
   - Возвращает обрезанную версию для отображения

МЕСТО РЕАЛИЗАЦИИ: html_group.py (добавить функции перед основным обработчиком)

ТЕСТИРОВАНИЕ:
- Проверить получение имени для разных типов пользователей
- Проверить корректность ссылок для разных chat_id
- Проверить обрезку промптов разной длины

РЕЗУЛЬТАТ ЭТАПА:
- Функции добавлены в html_group.py
- Все функции протестированы и работают корректно
- Документация к функциям написана

МЕСТО ДЛЯ ЗАПИСЕЙ ПО ВЫПОЛНЕНИЮ:
✅ ЭТАП 1 ВЫПОЛНЕН УСПЕШНО (23.07.2025)

ДОБАВЛЕННЫЕ ФУНКЦИИ В html_group.py (строки 340-423):

1. get_user_display_name(message) -> str
   - Реализована с приоритетом: username -> "имя фамилия" -> "имя" -> "Пользователь"
   - Протестирована для всех сценариев
   - Работает корректно

2. create_message_link(chat_id, message_id) -> str
   - Реализована обработка супергрупп (-100...) и обычных групп (-...)
   - Формат ссылки: https://t.me/c/{processed_chat_id}/{message_id}
   - Протестирована для разных типов chat_id
   - Работает корректно

3. truncate_prompt(prompt, max_words=8) -> str
   - Реализована обрезка промптов больше 8 слов с добавлением "..."
   - Поддерживает кастомное количество слов
   - Обрабатывает пустые промпты
   - Протестирована для всех сценариев
   - Работает корректно

ТЕСТИРОВАНИЕ:
- Создан файл test_mean_functions.py с полным покрытием тестами
- Все тесты прошли успешно (12 тестовых случаев)
- Проверены все граничные случаи и fallback сценарии

ДОКУМЕНТАЦИЯ:
- Все функции имеют подробные docstring с описанием параметров и возвращаемых значений
- Код соответствует стандартам проекта

ГОТОВНОСТЬ К ЭТАПУ 2: ✅ Все утилитарные функции готовы для использования

================================================================
ЭТАП 2: СОЗДАНИЕ СИСТЕМЫ OVERLAY
================================================================

ЗАДАЧИ:
1. Создать функцию generate_overlay_html(user_name, prompt_display, prompt_full, message_link)
   - Генерирует HTML код overlay с плашкой
   - Адаптирует стили из MEAN.html для overlay поверх сайта
   - Добавляет CSS с уникальными классами (префикс mean-overlay-)
   - Включает JavaScript для автоматического скрытия через 2 секунды

2. Структура overlay:
   - position: fixed, z-index: 9999, покрывает весь экран
   - Затемненный фон rgba(0,0,0,0.8)
   - Плашка по центру с размерами и стилями из MEAN.html
   - Анимация fade-out и удаление из DOM

3. Обработка безопасности:
   - HTML escape для промпта и имени пользователя
   - Защита от XSS атак
   - Валидация ссылки на сообщение

МЕСТО РЕАЛИЗАЦИИ: html_group.py (функция generate_overlay_html)

ТЕСТИРОВАНИЕ:
- Создать тестовый HTML файл с overlay
- Проверить отображение в браузере
- Проверить автоматическое скрытие через 2 секунды
- Проверить кликабельность ссылки на промпт

РЕЗУЛЬТАТ ЭТАПА:
- Функция generate_overlay_html создана и работает
- Overlay корректно отображается поверх любого сайта
- Анимация и автоскрытие работают стабильно
- Ссылки кликабельны и ведут на правильные сообщения

МЕСТО ДЛЯ ЗАПИСЕЙ ПО ВЫПОЛНЕНИЮ:
✅ ЭТАП 2 ВЫПОЛНЕН УСПЕШНО (23.07.2025)

ДОБАВЛЕННАЯ ФУНКЦИЯ В html_group.py (строки 425-563):

1. generate_overlay_html(user_name, prompt_display, prompt_full, message_link) -> str
   - Генерирует HTML код overlay с плашкой MEAN поверх сайта
   - Адаптирует стили из MEAN.html для overlay системы
   - Использует уникальные CSS классы с префиксом "mean-overlay-"
   - Включает JavaScript для автоматического скрытия через 2 секунды
   - Реализована защита от XSS атак через HTML escape
   - Поддерживает анимацию fade-out и удаление из DOM
   - Добавлена возможность закрытия кликом по фону

2. Структура overlay:
   - position: fixed, z-index: 9999, покрывает весь экран
   - Затемненный фон rgba(0,0,0,0.8)
   - Плашка по центру с размерами и стилями из MEAN.html (550x550px)
   - Адаптивность: max-width: 90vw, max-height: 90vh
   - Шрифт: 'Press Start 2P' (загружается из Google Fonts)

3. Безопасность:
   - HTML escape для всех пользовательских данных
   - Защита от XSS атак
   - Валидация и экранирование ссылок

ТЕСТИРОВАНИЕ:
- Создан файл test_overlay_function.py с полным покрытием тестами
- Все тесты прошли успешно (4 основных теста + интеграционный тест)
- Проверены все сценарии: обычные данные, HTML символы, пустые данные, длинные данные
- Создан тестовый HTML файл test_overlay_real_data.html
- Проверено отображение в браузере - overlay корректно появляется и исчезает
- Проверена кликабельность ссылки на промпт
- Проверена анимация fade-out и автоматическое удаление из DOM

РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:
- ✅ Overlay корректно отображается поверх любого сайта
- ✅ Анимация и автоскрытие работают стабильно (2 секунды)
- ✅ Ссылки кликабельны и ведут на правильные сообщения
- ✅ Стиль плашки полностью соответствует MEAN.html
- ✅ HTML escape защищает от XSS атак
- ✅ Адаптивный дизайн работает на разных размерах экрана
- ✅ Возможность закрытия кликом по фону работает корректно

ГОТОВНОСТЬ К ЭТАПУ 3: ✅ Функция generate_overlay_html готова для интеграции

================================================================
ЭТАП 3: ИНТЕГРАЦИЯ В ОСНОВНУЮ ФУНКЦИЮ
================================================================

ЗАДАЧИ:
1. Создать функцию inject_overlay_into_html(original_html, overlay_html)
   - Парсит сгенерированный HTML
   - Находит тег <body> и добавляет overlay в начало
   - Обрабатывает случаи когда <body> не найден (fallback)
   - Возвращает модифицированный HTML

2. Модифицировать handle_html_command_async:
   - После получения HTML от API (строка ~687)
   - Извлечь данные пользователя и промпт
   - Сгенерировать overlay
   - Встроить overlay в HTML
   - Сохранить обратную совместимость

3. Обработка ошибок:
   - Если интеграция overlay не удалась, отправить оригинальный HTML
   - Логирование ошибок для отладки
   - Не прерывать основной процесс генерации

МЕСТО РЕАЛИЗАЦИИ: 
- Функция inject_overlay_into_html в html_group.py
- Модификация handle_html_command_async в html_group.py (строка ~687)

ТЕСТИРОВАНИЕ:
- Протестировать команду /html с разными промптами
- Проверить работу с разными типами пользователей
- Проверить корректность ссылок на сообщения
- Убедиться что оригинальная функциональность не нарушена

РЕЗУЛЬТАТ ЭТАПА:
- Все генерируемые HTML сайты содержат плашку MEAN
- Плашка корректно отображается и исчезает через 2 секунды
- Ссылки на промпты работают корректно
- Основная функциональность /html не нарушена
- Система стабильно работает для всех пользователей

МЕСТО ДЛЯ ЗАПИСЕЙ ПО ВЫПОЛНЕНИЮ:
_________________________________________________________________
_________________________________________________________________
_________________________________________________________________

================================================================
ТЕХНИЧЕСКИЕ ДЕТАЛИ
================================================================

СТРУКТУРА ССЫЛКИ НА СООБЩЕНИЕ:
- Для групп: https://t.me/c/{chat_id_processed}/{message_id}
- chat_id_processed = str(abs(chat_id))[3:] для супергрупп (-100...)
- chat_id_processed = str(abs(chat_id))[1:] для обычных групп (-...)

ФОРМАТ ОТОБРАЖЕНИЯ ПРОМПТА:
- Если промпт <= 8 слов: отображать полностью
- Если промпт > 8 слов: "первые 8 слов..."
- Ссылка всегда ведет на полное сообщение

ПРИОРИТЕТ ОТОБРАЖЕНИЯ ИМЕНИ:
1. @username (если есть)
2. "Имя Фамилия" (если есть имя)
3. "Имя" (если есть только имя)
4. "Пользователь" (fallback)

CSS ПРЕФИКСЫ:
- Все CSS классы с префиксом "mean-overlay-"
- Избежание конфликтов с существующими стилями сайта

JAVASCRIPT БЕЗОПАСНОСТЬ:
- Обернуть в IIFE для избежания конфликтов
- Использовать уникальные ID элементов

================================================================
ФАЙЛЫ ДЛЯ ИЗМЕНЕНИЯ
================================================================

ОСНОВНОЙ ФАЙЛ: html_group.py
- Добавить утилитарные функции (строки ~340-400)
- Добавить функцию генерации overlay (строки ~400-500)
- Добавить функцию интеграции (строки ~500-550)
- Модифицировать handle_html_command_async (строка ~687)

РЕФЕРЕНСНЫЙ ФАЙЛ: MEAN.html
- Использовать как основу для стилей плашки
- Адаптировать для overlay системы

================================================================
КРИТЕРИИ УСПЕХА
================================================================

1. ВСЕ генерируемые HTML сайты содержат плашку
2. Плашка отображается ровно 2 секунды при открытии
3. Информация о пользователе корректна (ник или имя)
4. Промпт отображается корректно (обрезка при необходимости)
5. Ссылка на промпт кликабельна и ведет на исходное сообщение
6. Стиль плашки соответствует MEAN.html
7. Основная функциональность /html не нарушена
8. Система работает стабильно для всех типов пользователей
9. Нет конфликтов с существующими стилями сайтов
10. Обработка ошибок не прерывает генерацию сайтов

================================================================
ПЛАН ТЕСТИРОВАНИЯ
================================================================

ПОСЛЕ КАЖДОГО ЭТАПА:
1. Запустить команду /html с тестовым промптом
2. Проверить корректность отображения плашки
3. Проверить работу ссылки на сообщение
4. Проверить автоматическое скрытие через 2 секунды
5. Проверить работу с разными типами пользователей
6. Убедиться в отсутствии ошибок в логах

ФИНАЛЬНОЕ ТЕСТИРОВАНИЕ:
1. Тестирование с короткими промптами (< 8 слов)
2. Тестирование с длинными промптами (> 8 слов)
3. Тестирование с пользователями без username
4. Тестирование с пользователями только с именем
5. Тестирование в разных группах
6. Проверка корректности всех ссылок на сообщения

================================================================
